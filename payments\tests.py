
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from .models import PaymentMethod, Payment
from consultations.models import Consultation

class PaymentFlowTests(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = get_user_model().objects.create_user(
            email='<EMAIL>', password='testpass', first_name='Test', last_name='User', role='end_user', is_verified=True, age_verified=True
        )
        self.client.login(email='<EMAIL>', password='testpass')
        self.psychologist = get_user_model().objects.create_user(
            email='<EMAIL>', password='testpass', first_name='Psych', last_name='ologist', role='psychologist', is_verified=True, psychologist_approved=True, age_verified=True
        )
        self.consultation = Consultation.objects.create(
            user=self.user,
            psychologist=self.psychologist,
            type='paid',
            topic='Test Topic',
            description='Test Description',
            status='pending'
        )
        self.payment_method = PaymentMethod.objects.create(
            name='Chapa', provider='chapa', is_active=True, configuration={'secret_key': 'test_chapa_key'}
        )

    def test_payment_initiation(self):
        url = reverse('initiate_payment', args=[self.consultation.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Pay Now')

    def test_payment_post_redirect(self):
        url = reverse('initiate_payment', args=[self.consultation.id])
        response = self.client.post(url, {
            'payment_method': self.payment_method.id
        })
        self.assertIn(response.status_code, [302, 200])

    def test_chapa_callback(self):
        payment = Payment.objects.create(
            user=self.user,
            consultation=self.consultation,
            payment_method=self.payment_method,
            amount=500,
            currency='ETB',
            status='pending'
        )
        callback_url = reverse('payment_callback')
        data = {
            'tx_ref': str(payment.payment_id),
            'status': 'success'
        }
        response = self.client.post(callback_url, data, content_type='application/json')
        self.assertEqual(response.status_code, 200)
