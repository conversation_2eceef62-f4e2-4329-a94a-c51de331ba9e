{% extends "base.html" %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Professional Mental Health Support" %} - Psychology Platform{% endblock %}

{% block extra_css %}
<style>
    .hero-section {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        padding: 6rem 0;
        position: relative;
        overflow: hidden;
    }
    
    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
    }
    
    .feature-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-lg);
        transition: all 0.3s ease;
        height: 100%;
        border: 1px solid var(--gray-200);
    }
    
    .feature-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: 1.5rem;
        margin-bottom: var(--spacing-lg);
    }
    
    .stats-section {
        background: var(--gray-50);
        padding: 4rem 0;
    }
    
    .stat-item {
        text-align: center;
        padding: var(--spacing-lg);
    }
    
    .stat-number {
        font-size: 3rem;
        font-weight: 700;
        color: var(--primary-blue);
        margin-bottom: 0.5rem;
    }
    
    .cta-section {
        background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 100%);
        color: var(--white);
        padding: 4rem 0;
    }
    
    .hero-image-placeholder {
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-xl);
        padding: 3rem;
        backdrop-filter: blur(10px);
    }
    
    .section-padding {
        padding: 4rem 0;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center hero-content">
            <div class="col-lg-6 mb-5 mb-lg-0">
                <h1 class="display-4 fw-bold mb-4">
                    {% trans "Professional Psychology Consultation Platform" %}
                </h1>
                <p class="lead mb-4">
                    {% trans "Connect with licensed psychologists for professional mental health support. Book consultations, access resources, and join our supportive community." %}
                </p>
                <div class="d-flex flex-column flex-sm-row gap-3">
                    <a href="{% url 'register' %}" class="btn btn-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>
                        {% trans "Get Started" %}
                    </a>
                    <a href="{% url 'psychologist_list' %}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-search me-2"></i>
                        {% trans "Find Psychologists" %}
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <div class="hero-image-placeholder">
                        <i class="fas fa-brain fa-10x text-white opacity-75"></i>
                        <p class="mt-3 mb-0 text-white-50">{% trans "Mental Health Support" %}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<div class="landing-hero" style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: #fff; padding: 5rem 0 3rem 0; text-align: center; position: relative;">
    <img src="{% static 'images/logo-white.png' %}" alt="ECPI Logo" class="logo" style="height:64px; margin-bottom:1.5rem;">
    <h1 style="font-size:2.8rem; font-weight:700; margin-bottom:1rem;">Welcome to ECPI</h1>
    <p style="font-size:1.25rem; margin-bottom:2rem;">Your trusted platform for psychological consultation, peer support, and mental health resources in Ethiopia.</p>
    <a href="{% url 'register' %}" class="cta-btn" style="font-size:1.2rem; padding:0.75rem 2.5rem; border-radius:30px; font-weight:600; background:#fff; color:#2563eb; border:none; box-shadow:0 4px 16px rgba(37,99,235,0.08); transition:background 0.2s, color 0.2s;">Get Started</a>
</div>
<div class="features" style="background:#f8fafc; padding:3rem 0;">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-4 mb-4">
                <div class="feature-icon" style="font-size:2.5rem; color:#2563eb; margin-bottom:1rem;"><i class="fas fa-user-md"></i></div>
                <div class="feature-title" style="font-size:1.2rem; font-weight:600; margin-bottom:0.5rem;">Licensed Psychologists</div>
                <div class="feature-desc" style="color:#475569; font-size:1rem;">Book 1-on-1 sessions with verified professionals.</div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="feature-icon" style="font-size:2.5rem; color:#2563eb; margin-bottom:1rem;"><i class="fas fa-comments"></i></div>
                <div class="feature-title" style="font-size:1.2rem; font-weight:600; margin-bottom:0.5rem;">Peer Discussions</div>
                <div class="feature-desc" style="color:#475569; font-size:1rem;">Join forums and share experiences with others.</div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="feature-icon" style="font-size:2.5rem; color:#2563eb; margin-bottom:1rem;"><i class="fas fa-book"></i></div>
                <div class="feature-title" style="font-size:1.2rem; font-weight:600; margin-bottom:0.5rem;">Resource Center</div>
                <div class="feature-desc" style="color:#475569; font-size:1rem;">Access multimedia resources for mental wellness.</div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">{% trans "Why Choose Our Platform?" %}</h2>
                <p class="lead text-muted">{% trans "We provide comprehensive mental health support with professional care and modern technology." %}</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <h4 class="mb-3">{% trans "Licensed Professionals" %}</h4>
                    <p class="text-muted">{% trans "All our psychologists are licensed and verified professionals with years of experience in mental health care." %}</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h4 class="mb-3">{% trans "Easy Booking" %}</h4>
                    <p class="text-muted">{% trans "Book consultations easily with our calendar-based system. Choose from available time slots that work for you." %}</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 class="mb-3">{% trans "Secure & Private" %}</h4>
                    <p class="text-muted">{% trans "Your privacy is our priority. All consultations and data are encrypted and completely confidential." %}</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h4 class="mb-3">{% trans "Community Support" %}</h4>
                    <p class="text-muted">{% trans "Join discussion forums and connect with others on similar journeys. Share experiences and find support." %}</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-book"></i>
                    </div>
                    <h4 class="mb-3">{% trans "Rich Resources" %}</h4>
                    <p class="text-muted">{% trans "Access a library of mental health resources, guides, and tools created by our professional psychologists." %}</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h4 class="mb-3">{% trans "AI Assistant" %}</h4>
                    <p class="text-muted">{% trans "Get instant answers to common questions with our intelligent chatbot available 24/7." %}</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="stats-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <div class="stat-number">500+</div>
                    <h5>{% trans "Happy Clients" %}</h5>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <h5>{% trans "Licensed Psychologists" %}</h5>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <div class="stat-number">1000+</div>
                    <h5>{% trans "Consultations Completed" %}</h5>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <h5>{% trans "Support Available" %}</h5>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">{% trans "How It Works" %}</h2>
                <p class="lead text-muted">{% trans "Getting started with professional mental health support is easy." %}</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-4 text-center">
                <div class="mb-4">
                    <div class="feature-icon mx-auto">
                        <span class="fw-bold">1</span>
                    </div>
                </div>
                <h4 class="mb-3">{% trans "Create Account" %}</h4>
                <p class="text-muted">{% trans "Sign up for free and complete your profile. Age verification ensures a safe environment for all users." %}</p>
            </div>
            
            <div class="col-lg-4 text-center">
                <div class="mb-4">
                    <div class="feature-icon mx-auto">
                        <span class="fw-bold">2</span>
                    </div>
                </div>
                <h4 class="mb-3">{% trans "Find Your Psychologist" %}</h4>
                <p class="text-muted">{% trans "Browse our verified psychologists, read their profiles, and choose the one that best fits your needs." %}</p>
            </div>
            
            <div class="col-lg-4 text-center">
                <div class="mb-4">
                    <div class="feature-icon mx-auto">
                        <span class="fw-bold">3</span>
                    </div>
                </div>
                <h4 class="mb-3">{% trans "Book & Connect" %}</h4>
                <p class="text-muted">{% trans "Schedule your consultation and start your journey to better mental health with professional support." %}</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-5 fw-bold mb-4">{% trans "Ready to Start Your Mental Health Journey?" %}</h2>
                <p class="lead mb-4">{% trans "Join thousands of people who have found support and guidance through our platform." %}</p>
                <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                    <a href="{% url 'register' %}" class="btn btn-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>
                        {% trans "Register Now" %}
                    </a>
                    <a href="{% url 'chatbot_interface' %}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-robot me-2"></i>
                        {% trans "Try AI Assistant" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate statistics on scroll
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateNumber(entry.target);
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.stat-number').forEach(el => {
        observer.observe(el);
    });
    
    function animateNumber(element) {
        const finalNumber = element.textContent.replace(/[^\d]/g, '');
        if (!finalNumber) return;
        
        const duration = 2000;
        const increment = finalNumber / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= finalNumber) {
                element.textContent = element.textContent.replace(/\d+/, finalNumber);
                clearInterval(timer);
            } else {
                element.textContent = element.textContent.replace(/\d+/, Math.floor(current));
            }
        }, 16);
    }
});
</script>
{% endblock %}
